/**
 * 知识图谱专用控制器 - KsgControls.ts
 *
 * 职责：
 * 1. 管理3D场景中的相机控制逻辑
 * 2. 支持多种控制模式（知识点层/领域层）
 * 3. 提供平滑的交互体验（阻尼、限制等）
 * 4. 处理鼠标、触摸、键盘等多种输入方式
 * 5. 集成自动旋转和控制状态检测
 *
 * 技术特点：
 * - 基于Three.js的球面坐标系统
 * - 支持多点触控操作
 * - 自适应Y轴范围限制
 * - 事件驱动的架构设计
 * - 高性能的实时更新机制
 */
import { EventDispatcher, // 事件分发器基类，用于实现观察者模式
PerspectiveCamera, // 透视相机，模拟人眼视觉效果
MOUSE, // 球面坐标系，用于相机轨道控制
TOUCH, // 二维向量，用于屏幕坐标计算
Vector3, // 三维向量，用于3D空间坐标
type BaseEvent, // 4x4矩阵，用于3D变换
Group, // 3D对象组
Object3D } from "three";
/**
 * 控制器模式枚举
 * 定义了不同的相机控制模式，适应不同的使用场景
 */
declare enum KsgMode {
    /** 知识点层模式 - 标准的节点浏览模式 */
    Star = 0,
    /** 领域层模式 - 领域级别的宏观控制模式 */
    Domain = 1
}
/**
 * 知识图谱专用控制器类
 * 继承自Three.js的EventDispatcher，支持事件驱动的交互模式
 */
declare class KsgControls extends EventDispatcher<{
    [key: string]: BaseEvent;
}> {
    /** 控制的透视相机对象 */
    object: PerspectiveCamera;
    /** 根领域对象组 - 用于领域层模式的控制 */
    rootAreaObj: Group;
    /** 子领域对象 - 领域模式下的控制目标 */
    subareas: Object3D | null;
    /** DOM元素 - 用于事件监听和交互 */
    domElement: HTMLElement;
    /** 控制器总开关 */
    enabled: boolean;
    /** 相机观察目标点 - 相机总是朝向这个点 */
    target: Vector3;
    /** 相机距离目标的最小值 - 防止过度靠近 */
    minDistance: number;
    /** 相机距离目标的最大值 - 防止无限远离 */
    maxDistance: number;
    /** 极角的最小值 - 限制垂直旋转范围 */
    minPolarAngle: number;
    /** 极角的最大值 - 限制垂直旋转范围 */
    maxPolarAngle: number;
    /** 是否启用阻尼效果 - 提供平滑的控制体验 */
    enableDamping: boolean;
    /** 阻尼系数 - 控制减速的程度 */
    dampingFactor: number;
    /** 是否允许缩放操作 */
    enableZoom: boolean;
    /** 缩放速度倍数 */
    zoomSpeed: number;
    /** 是否允许旋转操作 */
    enableRotate: boolean;
    /** 旋转速度倍数 */
    rotateSpeed: number;
    /** 是否允许平移操作 */
    enablePan: boolean;
    /** 平移速度倍数 */
    panSpeed: number;
    /** 当前控制模式 */
    mode: KsgMode;
    /** 键盘按键映射 */
    keys: {
        LEFT: string;
        UP: string;
        RIGHT: string;
        BOTTOM: string;
    };
    /** 鼠标按键功能映射 */
    mouseButtons: {
        LEFT: MOUSE;
        MIDDLE: MOUSE;
        RIGHT: MOUSE;
    };
    /** 触摸手势映射 */
    touches: {
        ONE: TOUCH;
        TWO: TOUCH;
    };
    /** 键盘控制的移动速度 */
    keyPanSpeed: number;
    /** 相机Y轴的最小范围 - 适应知识图谱的纵向结构 */
    yMinRange: number;
    /** 相机Y轴的最大范围 */
    yMaxRange: number;
    /** 目标点Y轴的范围偏移量 */
    yDelta: number;
    /** Y轴方向向量 */
    yAxis: Vector3;
    /** 是否启用自动旋转 */
    autoRotate: boolean;
    /** 自动旋转的速度 */
    autoRotateSpeed: number;
    /** 是否正在进行控制操作 */
    isControls: boolean;
    /** 控制状态检测的计时器 */
    controlsTimer: number | NodeJS.Timeout | null;
    /** 键盘事件监听的DOM元素 */
    _domElementKeyEvents: HTMLElement | null;
    /** 更新函数 - 每帧调用以应用控制变化 */
    update: (deltaTime?: number | null) => boolean;
    /** 清理函数 - 移除所有事件监听器 */
    dispose: () => void;
    /** 模式切换函数 - 在不同控制模式间切换 */
    changeMode: (mode: KsgMode) => void;
    /**
     * 构造函数 - 初始化知识图谱控制器
     *
     * @param object 要控制的透视相机对象
     * @param rootArea 根领域对象组
     * @param domElement 用于事件监听的DOM元素
     */
    constructor(object: PerspectiveCamera, rootArea: Group, domElement: HTMLElement);
    /**
     * 自动旋转函数,若要开启自动旋转功能,请设置autoRotate为true
     * 渲染帧中调用此方法
     */
    autoRotateUpdate(deltaTime: number): void;
}
export { KsgMode, KsgControls };
