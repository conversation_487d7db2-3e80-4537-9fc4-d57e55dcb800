/**
 * 该文件负责管理 KsgMap 组件中 Three.js 相关的所有默认配置项，
 * 包括相机、渲染器、场景、控制器等核心配置以及知识图谱特有的配置项。
 */
import type { CameraConfig, RendererConfig, SceneConfig, ControlsConfig } from "../types/index";
import { MODE } from "../enums";
/**
 * 初始化 Three.js 配置的 Hook 函数
 *
 * 该函数接收用户自定义配置选项，与默认配置进行合并，
 * 并更新全局上下文，最后返回格式化后的配置对象。
 *
 * @param option - 用户自定义配置选项
 * @returns 包含各模块配置的对象
 *
 * @example
 * ```typescript
 * const config = useInitThreeJsConfig({
 *   camera: { fov: 60 },
 *   renderer: { width: 800, height: 600 }
 * });
 * ```
 */
export declare function useInitThreeJsConfig(option?: Options): {
    /** 相机配置 */
    cameraConfig: any;
    /** 渲染器配置 */
    renderConfig: any;
    /** 场景配置 */
    sceneConfig: any;
    /** 控制器配置 */
    controlsConfig: any;
    /** 包装容器尺寸配置 */
    wrapperEleSizeConfig: {
        width: number;
        height: number;
    };
};
/**
 * KsgMap 配置选项类型定义
 *
 * 定义了所有可配置的选项类型，用户可以通过传入这些选项来自定义组件行为。
 * 所有属性都是可选的，未配置的属性将使用默认值。
 */
export type Options = {
    /** 显示模式配置 */
    model?: MODE;
    /** 视口范围配置 */
    viewRange?: {
        /** 最小X坐标 */
        minX: number;
        /** 最大X坐标 */
        maxX: number;
        /** 最小Y坐标 */
        minY: number;
        /** 最大Y坐标 */
        maxY: number;
    };
    /** 相机配置参数 */
    camera?: CameraConfig;
    /** 渲染器配置参数 */
    renderer?: RendererConfig;
    /** 场景配置参数 */
    scene?: SceneConfig;
    /** 控制器配置参数 */
    controls?: ControlsConfig;
    /** 知识节点分页查询配置 */
    pointsLevelPager?: {
        /** 当前显示层级 */
        current: number;
        /** 总层级数量 */
        levelSize: number;
        /** 节点总数（可选） */
        total?: number;
    };
    /** 层级间隔距离 */
    levelSpace?: number;
    /** 知识节点相关配置 */
    point?: {
        /** 节点半径大小 */
        radius: number;
        /** 节点之间的间距 */
        space: number;
    };
    /** 连线流光效果相关配置 */
    line?: {
        /** 流光长度 */
        length?: number;
        /** 流光移动速度 */
        speed?: number;
        /** 多条连线流光效果是否随机播放 */
        isRandom?: boolean;
    };
    /** 悬停弹窗位置偏移配置 */
    hoverLabel?: {
        /** X轴偏移量（像素） */
        offsetX: number;
        /** Y轴偏移量（像素） */
        offsetY: number;
    };
    /** 弹窗显示距离限制 */
    maxDistance?: number;
    /** 节点之间的间距 */
    pointSpace?: number;
    /** 回退到根节点的回调函数 */
    focusBackToRoot?: () => void;
    /** 回退到父级节点的回调函数 */
    focusBack?: () => void;
};
