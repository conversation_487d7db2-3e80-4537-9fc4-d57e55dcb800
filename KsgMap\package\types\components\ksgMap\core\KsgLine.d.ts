import { LineSegments } from "three";
import { Point } from "../types";
export default class KsgLine2 extends LineSegments {
    /** 流光动画开关 - 控制是否启用流光效果 */
    enableAnimation: boolean;
    /** 流光移动速度 - 控制流光动画的播放速度 */
    speed: number;
    /** 位置数据缓存 - 存储所有线条顶点的3D坐标 */
    posit: number[];
    /** 颜色数据缓存 - 存储所有线条顶点的RGB颜色值 */
    colors: number[];
    /** 索引数据缓存 - 存储线条的索引信息 */
    indexs: number[];
    /** 线段进度缓存 - 存储每个顶点在线段上的位置比例(0-1) */
    segmentProgress: number[];
    /** 随机数缓存 - 为每条线分配随机值，用于随机流光效果 */
    random: number[];
    constructor(focusPoint: Point, focusChildren: Point[], opacity?: number);
    /**
     * 释放内存
     */
    dispose(): void;
    /**
     * 末位置更新函数
     */
    updateEndPosition(index: number, position: [number, number, number]): void;
    updateFocusPointPosition(position: [number, number, number]): void;
    /**
     * 更新透明度
     */
    updateOpacity(opacity: number): void;
    /**
     * 线条流光动画
     */
    update(): void;
    /**
     * 聚焦节点添加连线
     * @param newFocusChildren 增加聚焦节点的直接前驱节点
     */
    addFocusChildren(newFocusChildren: Point[]): void;
}
