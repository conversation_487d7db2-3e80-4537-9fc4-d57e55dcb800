import { CSS2DObject } from "three/examples/jsm/renderers/CSS2DRenderer.js";
import type { Point, ViewRange } from "../types";
/**
 * 知识节点标签类
 * 继承自Three.js的CSS2DObject，支持3D场景中的2D HTML渲染
 */
export declare class KsgLabel extends CSS2DObject {
    /** 标签宽度 - 用于位置计算 */
    labelWidth: number;
    /** 标签高度 - 用于位置计算 */
    labelHeight: number;
    /** 当前绑定的节点对象 */
    point: Point | null;
    /** 上一次显示的节点索引 - 用于避免重复处理 */
    lastIndex: number | null;
    /** 标签相对节点的偏移位置 */
    offset: {
        x: number;
        y: number;
    };
    /**
     * 构造函数 - 初始化标签对象
     *
     * @param offset 标签偏移量 - 相对于节点位置的像素偏移
     */
    constructor(offset?: {
        x: number;
        y: number;
    });
    /**
     * 显示标签并绑定到指定节点
     *
     * 执行流程：
     * 1. 检查是否为重复显示（优化性能）
     * 2. 设置标签内容和样式
     * 3. 渲染MathJax数学公式
     * 4. 计算并设置最佳显示位置
     * 5. 显示标签并缓存节点信息
     *
     * @param point 要绑定的节点对象
     * @param option 可选的位置计算参数
     */
    display(point: Point, option?: {
        viewRange: ViewRange;
        dnc: {
            x: number;
            y: number;
        };
    }): void;
    /**
     * 智能定位算法 - 确保标签不会超出视口边界
     *
     * 算法流程：
     * 1. 计算标签的实际尺寸
     * 2. 检测水平方向的边界冲突
     * 3. 检测垂直方向的边界冲突
     * 4. 根据冲突情况选择最佳位置
     * 5. 应用相应的CSS样式和锚点设置
     *
     * @param option 位置计算参数
     * @returns 最终选择的位置字符串
     */
    private setPosition;
    /**
     * 隐藏标签并清理状态
     *
     * 在鼠标移出节点或切换标签目标时调用
     */
    hide(): void;
    /**
     * 距离控制显示效果
     *
     * 根据相机距离动态调整标签的可见性
     * 近距离时显示，远距离时隐藏，提供更好的视觉体验
     *
     * @param show 是否显示标签
     */
    distanceShow(show: boolean): void;
    /**
     * 更新标签位置
     *
     * 用于节点位置发生变化时同步标签位置
     * 通常在动画过程中调用
     *
     * @param position 新的3D坐标 [x, y, z]
     */
    updatePosition(position: [number, number, number]): void;
}
/** 悬停标签 - 用于鼠标悬停时显示节点信息 */
declare const hoverLabel: KsgLabel;
/** 聚焦标签 - 用于聚焦状态下显示节点信息 */
declare const focusLabel: KsgLabel;
export { hoverLabel, focusLabel };
