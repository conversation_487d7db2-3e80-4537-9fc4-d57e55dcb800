/**
 * 分帧处理调度器
 * 用于将大量计算任务分散到多个渲染帧中执行，避免阻塞主线程
 * 适用于处理大数据集、复杂计算等可能导致页面卡顿的操作
 */
export default class FrameScheduler {
    /** 任务队列，存储待执行的任务函数 */
    private taskQueue;
    /** 标记调度器是否正在运行 */
    private isRunning;
    /** 所有任务完成后的回调函数 */
    completedCallback: () => void;
    /**
     * 添加任务到队列
     * @param task 待执行的任务函数
     *             - 返回 true: 表示所有任务已完成，停止调度
     *             - 返回 false: 表示还有任务需要继续执行
     */
    addTask(task: () => boolean): void;
    /**
     * 分帧计算函数，此函数在渲染帧中调用，调用一次则执行一次处理任务
     * 使用 requestAnimationFrame 确保任务在下一个渲染帧执行，避免阻塞当前帧
     */
    runNextTask(): void;
    /**
     * 清空任务队列
     * 立即停止所有未执行的任务，重置调度器状态
     */
    clearTasks(): void;
    /**
     * 设置任务完成回调函数
     * @param callback 当所有任务执行完成时触发的回调函数
     */
    onCompleted(callback: () => void): void;
}
