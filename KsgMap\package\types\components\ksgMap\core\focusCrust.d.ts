import { InstancedMesh } from "three";
import { Point } from "../types";
/**
 * 聚焦外壳类 - 继承自Three.js的InstancedMesh
 *
 * 使用实例化网格技术，即使只渲染一个外壳也保持架构的一致性
 * 支持未来扩展为多个聚焦点的场景
 */
export declare class FocusCrust extends InstancedMesh {
    /** 上一个绑定的聚焦节点 - 用于避免重复绑定和状态管理 */
    lastPoint: Point | null;
    /**
     * 构造函数 - 初始化聚焦外壳
     *
     * @param size 外壳大小倍数 - 相对于节点的放大系数
     * @param opacity 透明度 - 外壳的整体透明度 (0-1)
     */
    constructor(size: number, opacity?: number);
    /**
     * 渲染帧更新函数 - 在每一帧中调用
     *
     * 负责推进纹理动画的时间进度
     * 只有在外壳可见时才执行，优化性能
     *
     * @param delta 时间增量 - 两帧之间的时间差
     */
    update(delta?: number): void;
    /**
     * 显示外壳并绑定到指定节点
     *
     * 执行流程：
     * 1. 设置外壳位置到节点坐标
     * 2. 根据节点状态设置颜色
     * 3. 显示外壳并缓存节点引用
     *
     * @param bindPoint 要绑定的聚焦节点对象
     */
    display(bindPoint: Point): void;
    /**
     * 隐藏外壳并清理状态
     *
     * 在退出聚焦模式或切换聚焦节点时调用
     */
    hide(): void;
    /**
     * 更新外壳位置
     *
     * 用于节点位置发生变化时同步外壳位置
     * 通常在动画过程中调用
     *
     * @param position 新的3D坐标 [x, y, z]
     */
    updatePosition(position: [number, number, number]): void;
}
declare const focusCrust: FocusCrust;
export default focusCrust;
