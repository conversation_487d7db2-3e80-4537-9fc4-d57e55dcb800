import type { PointData, Point } from "../types";
import FrameScheduler from "../utils/FrameScheduler";
export declare const frameScheduler: FrameScheduler;
/**
 * 节点变化数据类型 - 用于记录节点在更新过程中的状态变化
 * 主要用于增量更新和动画过渡效果
 */
export type ModifyPoint = {
    /** 更新前的节点状态 - 保存原始位置和属性 */
    old: Point;
    /** 更新后的节点状态 - 保存新的位置和属性（可选） */
    new?: Point;
};
/**
 * 差异数据类型 - 用于描述数据更新时的变化情况
 * 支持增量更新，避免重新计算整个图结构
 */
export type DiffData = {
    /** 新增的节点列表 - 首次加载或新增的节点 */
    newPoints: Point[];
    /** 位置发生变化的节点映射 - key为节点ID，value为变化前后的状态 */
    updatePoints: Map<string, ModifyPoint>;
};
/**
 * 知识图谱空间布局计算器 - KsgGraph类
 *
 * 这是知识图谱的核心计算引擎，负责将原始的节点数据转换为3D空间中的坐标布局
 * 实现了DAG（有向无环图）的层次化布局算法，确保知识节点按照逻辑关系有序排列
 *
 * 主要功能：
 * - 解析节点间的父子关系，构建图结构
 * - 计算节点的层级分布（BFS层次遍历）
 * - 计算每个节点在3D空间中的精确坐标
 * - 支持增量数据加载和位置更新
 * - 提供异步计算能力，避免阻塞UI线程
 *
 * 算法特点：
 * - 使用广度优先搜索确定节点层级
 * - 同层节点均匀分布，避免重叠
 * - 支持多根节点的复杂图结构
 * - 优化的增量更新算法，提高性能
 */
export default class KsgGraph {
    /**
     * 节点数据映射表 - 存储所有处理后的节点信息
     * key: 节点ID, value: 包含坐标和关系信息的Point对象
     */
    pointsData: Map<string, Point>;
    /**
     * 层级索引映射 - 按层级组织节点ID
     * key: 层级编号(0,1,2...), value: 该层级的所有节点ID数组
     * 用于快速查找同层节点和层级遍历
     */
    idLevelMap: {
        [level: number]: string[];
    };
    /**
     * 增量更新的差异数据 - 记录数据变化情况
     * 用于支持懒加载和动画过渡效果
     */
    diffData: DiffData;
    /**
     * 构造函数 - 初始化知识图谱布局计算器
     * 接收原始节点数据并立即开始计算布局
     *
     * @param pointsData 原始节点数据数组，包含节点ID、名称、父子关系等信息
     */
    constructor(pointsData: PointData[]);
    /**
     * 计算节点布局的主流程函数
     * 按照标准的图布局算法流程执行：构建图 -> 计算层级 -> 计算坐标
     *
     * 执行步骤：
     * 1. 构建图结构（解析节点关系，建立父子映射）
     * 2. 计算节点层级（使用BFS算法确定每个节点的层级位置）
     * 3. 计算节点3D坐标（异步执行，避免阻塞UI线程）
     *
     * @param pointsData 原始节点数据数组
     */
    compute(pointsData: PointData[]): void;
    /**
     * 加载更多数据的增量更新函数
     * 支持在现有图结构基础上添加新节点，并处理位置变化
     *
     * @param pointsData 新增的节点数据数组
     * @returns Promise<DiffData> 返回包含新增和更新节点信息的差异数据
     */
    loadMore(pointsData: PointData[]): Promise<DiffData>;
    /**
     * 构建图数据结构的私有方法
     * 将原始节点数据转换为内部的Point对象，并建立完整的父子关系网络
     *
     * 处理流程：
     * 1. 创建或更新Point对象
     * 2. 建立父子关系映射
     *
     * @param pointsData 原始节点数据数组
     */
    private build;
    /**
     * 计算节点层级的方法
     * 使用拓扑排序算法（基于Kahn算法）计算DAG中每个节点的层级
     * 确保父节点总是在子节点的上层
     *
     * @param points 节点数据映射表
     */
    computeLevel(points: Map<string, Point>): void;
    /**
     * 计算每层节点的3D空间位置
     * 使用同心圆布局算法，确保同层节点均匀分布，避免重叠
     *
     * @param levelHeight 层级间的垂直间距（默认15个单位）
     * @param pointSpace 同层节点间的径向间距（默认7个单位）
     */
    computePointPosition(levelHeight?: number, pointSpace?: number): void;
    /**
     * 检查节点位置是否发生变化的私有方法
     * 用于增量更新时判断哪些节点需要执行位置变化动画
     *
     * @param id 节点ID
     * @param coordinate 新的坐标位置 [x, y, z]
     * @returns boolean 如果位置发生变化返回true，否则返回false
     */
    private isPositionChange;
    /**
     * 根据节点ID获取Point对象的公共方法
     * 提供安全的节点查找功能，避免直接操作内部数据结构
     *
     * @param id 节点的唯一标识符
     * @returns Point对象或null（如果节点不存在）
     */
    getPointById(id: string): Point | null;
    /**
     * 获取当前图的最大层级深度
     * 用于确定图的整体结构深度，层级从0开始计算
     *
     * @returns number 最大层级编号（从0开始）
     */
    getMaxLevel(): number;
}
