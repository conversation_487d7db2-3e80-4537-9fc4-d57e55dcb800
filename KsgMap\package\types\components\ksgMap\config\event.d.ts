import { LOAD_STATUS } from "../enums";
import type { EventsCallback, Size } from "../types";
/**
 * 事件系统初始化函数 - 知识图谱交互事件的核心管理器
 *
 * 这个函数是整个知识图谱交互系统的核心，负责管理所有的用户交互事件
 * 包括鼠标悬停、点击、双击、拖拽等各种交互行为的处理
 *
 * 主要功能：
 * - 节点悬停效果和标签显示
 * - 节点点击进入聚焦模式
 * - 双击进入全局视角
 * - 相机控制器事件管理
 * - 数据懒加载触发
 * - 视角切换和历史记录管理
 *
 * @param wrapperElSize 容器元素的尺寸信息
 * @param events 外部事件回调函数集合
 * @returns 返回事件管理相关的函数集合
 */
export default function useInitEvents(wrapperElSize: Size, events: EventsCallback): {
    initEvents: (containerEle: HTMLElement) => void;
    destroyEvents: () => void;
    focusBackToRoot: () => void;
    focusBack: () => void;
    updateClickEventSize: (w: number, h: number) => void;
    updateHoverEventSize: (w: number, h: number) => void;
    changeLoadStatus: (status: LOAD_STATUS) => void;
};
