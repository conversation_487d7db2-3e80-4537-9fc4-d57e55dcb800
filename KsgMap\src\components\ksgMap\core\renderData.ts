// 导入上下文对象，包含图数据、相机、控制器等全局状态
import ctx from "../ctx";
// 导入类型定义：Point表示知识节点，FocusData表示聚焦数据
import { type Point, type FocusData } from "../types";
// 导入节点和连线的进入/离开动画函数
import {
  pointEnterAnimation, // 节点进入动画
  lineEnterAnimation, // 连线进入动画
  lineLeaveAnimation, // 连线离开动画
} from "../animation/load";
// 导入视角移动动画
import { viewMoveAnimation } from "../animation/enterFocus";
// 导入节点位置更新动画
import { updatePointPositionAnimation } from "../animation/loadMore";
// 导入核心渲染组件
import KsgPoint from "./KsgPoints"; // 节点渲染器
import KsgLine from "./KsgLine"; // 连线渲染器
import focusCrust from "./focusCrust"; // 聚焦外壳效果
import { focusLabel } from "./KsgLabel"; // 聚焦标签
import focusChildrenLabelManager from "./FocusChildrenLabelManager"; // 子节点标签管理器
// 导入枚举类型：视图模式和数据模式
import { VIEW_MODE, MODE } from "../enums";
// 导入差异数据类型，用于增量更新
import type { DiffData, ModifyPoint } from "./KsgGraph";
// 导入全局视图切换函数和全局标签管理器
import enterGlobalView from "./enterGlobalView";
import { globalLabelManager } from "../utils/globalViewLabelManager";
// 导入鼠标移动事件处理器
import { createMouseMoveEvent } from "../utils/globalViewEvent";

// 聚焦节点的直接子节点列表缓存
// 用于记录当前聚焦节点的所有子节点，便于连线渲染和状态管理
let lastFocusChildPoints: Point[] = [];

/**
 * 第一次渲染单根节点数据（且聚焦根节点）
 * 用于初始化显示单个根节点的知识图谱，并自动聚焦到根节点
 * 包含节点动画、连线动画和聚焦效果的完整流程
 */
export function firstRenderSignalRootPoints() {
  // 初始化动画队列相关变量
  const points: Point[] = []; // 存储所有节点的数组
  const pointAnimationQue: Point[][] = []; // 按层级组织的节点动画队列

  // 获取当前聚焦的根节点对象
  const focusPoint = ctx.graph?.getPointById(ctx.focusPointInfo!.pointId)!;

  // 按层级构建动画队列，每个层级为一个数组
  for (const levelKey in ctx.graph!.idLevelMap) {
    pointAnimationQue.push(
      // 将当前层级的所有节点ID转换为Point对象
      ctx.graph!.idLevelMap[levelKey].map((id) => ctx.graph!.getPointById(id)!)
    );
  }

  // 缓存聚焦节点的所有子节点，用于后续连线渲染
  lastFocusChildPoints = focusPoint.childIds.map(
    (id) => ctx.graph!.getPointById(id)!
  );

  // 收集所有节点数据到points数组中
  ctx.graph?.pointsData.forEach((point) => points.push(point));

  // 创建节点渲染器，传入节点数据、总数、起始索引和层级高度
  ctx.pointsMesh = new KsgPoint(points, ctx.pointsLevelPager!.total, 0, 20);
  // 将节点渲染器添加到视图组中
  ctx.viewGroup!.add(ctx.pointsMesh);
  
  // 建立聚焦子节点的索引集合，用于快速查找
  ctx.focusChildrenIndex = new Set(
    lastFocusChildPoints.map((point) => point.index!)
  );

  // 处理聚焦节点的特殊显示
  const rootPoint = pointAnimationQue.shift()![0]; // 弹出根节点，不参与普通动画
  ctx.pointsMesh.toggleFocus(rootPoint.index!); // 切换节点为聚焦状态
  focusCrust.display(rootPoint); // 显示聚焦外壳效果
  focusLabel.display(rootPoint); // 显示聚焦标签
  ctx.focusStack?.push(rootPoint.id); // 将根节点加入历史栈

  // 启动节点进入动画，1200ms总时长
  startPointAnimation(pointAnimationQue, 1200)
    .then(() => {
      // 节点动画完成后创建连线渲染器
      ctx.focusLine = new KsgLine(rootPoint, lastFocusChildPoints, 0.1);
      ctx.viewGroup?.add(ctx.focusLine);

      // 并行执行所有子节点的连线进入动画
      return Promise.all(
        lastFocusChildPoints.map((point) =>
          lineEnterAnimation(ctx.focusLine!, rootPoint, point, 0.1)
        )
      );
    })
    .then(() => {
      // 连线动画完成后，切换子节点为聚焦状态
      ctx.pointsMesh?.toggleFocusChildren(
        lastFocusChildPoints.map((point) => point.index!)
      );
      // 启用连线的动态效果
      ctx.focusLine!.enableAnimation = true;
    });
}

/**
 * 第一次渲染多根节点数据（没有焦点）
 * 用于显示包含多个根节点的知识图谱，所有节点都处于同等重要性
 * 适用于展示多个独立的知识领域或主题
 *
 * 多根模式特点：
 * - 初始化完成后自动切换到全局视图模式
 * - 启用节点呼吸动画效果
 * - 显示全局标签管理器
 */
export function firstRenderMultiplyRootPoints() {
  // 初始化节点和动画队列
  const points: Point[] = []; // 存储所有节点
  const pointAnimationQue: Point[][] = []; // 按层级组织的动画队列

  // 收集所有节点数据
  ctx.graph?.pointsData.forEach((point) => points.push(point));

  // 按层级构建动画队列
  for (const levelKey in ctx.graph!.idLevelMap) {
    pointAnimationQue.push(
      // 将每个层级的节点ID转换为Point对象
      ctx.graph!.idLevelMap[levelKey].map((id) => ctx.graph!.getPointById(id)!)
    );
  }

  // 创建节点渲染器，多根模式下节点分布更均匀
  ctx.pointsMesh = new KsgPoint(
    [...points], // 复制节点数组
    ctx.pointsLevelPager!.total, // 总节点数
    0, // 起始索引
    20 // 层级高度
  );

  // 设置所有节点为高亮状态（多根模式下所有节点同等重要）
  ctx.pointsMesh.setHightLightPoints(points);
  // 将节点渲染器添加到场景中
  ctx.viewGroup!.add(ctx.pointsMesh);

  // 将第一个节点作为参考点加入历史栈
  ctx.focusStack!.push(points[0].id);

  // 启动节点动画，初始透明度为1（完全显示），持续1200ms
  // 动画完成后自动切换到全局视图模式
  startPointAnimation(pointAnimationQue, 1200, 1).then(() => {
    // 多根模式初始化完成后，自动切换到全局视图模式
    // 这样更符合多根节点的展示需求，用户可以总览整个知识图谱

    // 直接设置视图模式为全局视图
    ctx.viewMode = VIEW_MODE.GLOBAL_VIEW;

    // 启用节点呼吸动画，增强全局视图的视觉效果
    ctx.pointsMesh!.breathAnimationSwitch();

    // 启用全局视角标签管理器，智能显示节点标签
    globalLabelManager.enable();

    // 启用自动旋转功能
    ctx.controls!.autoRotate = true;
    ctx.controls!.autoRotateSpeed = 0.02; // 设置适中的旋转速度
    ctx.controls!.enabled = true;

    // 创建鼠标移动检测器，用于智能控制自动旋转的启停
    const onMove = () => {
      if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW && ctx.controls!.autoRotate) {
        ctx.controls!.autoRotate = false; // 鼠标移动时停止自动旋转
      }
    };

    const onMoveEnd = () => {
      if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW && !ctx.controls!.autoRotate) {
        ctx.controls!.autoRotate = true; // 鼠标停止移动后恢复自动旋转
      }
    };

    // 创建鼠标移动事件处理器，1秒无操作后恢复自动旋转
    const { handleMoveEvent } = createMouseMoveEvent(onMove, onMoveEnd, 1000);

    // 在CSS2D渲染器的DOM元素上添加鼠标移动监听
    // 使用CSS2D渲染器的DOM元素是因为它在HTML层级中位置更高，能正确接收鼠标事件
    const containerElement = ctx.css2dRenderer?.domElement.parentElement;
    if (containerElement) {
      containerElement.addEventListener("mousemove", handleMoveEvent);
    }

    // 清空历史栈并添加全局视图标记
    ctx.focusStack = ["null"];

    console.log("多根模式初始化完成，已自动切换到全局视图模式，启用自动旋转");
  });
}

/**
 * 知识点初始化动画控制函数
 * 按层级顺序播放节点的进入动画，创建层次感和流畅的视觉效果
 * @param animationQue 按层级组织的节点动画队列
 * @param totalDuration 总动画时长（毫秒）
 * @param initOpacity 节点初始透明度（0-1之间）
 * @returns Promise 当所有动画完成时resolve
 */
function startPointAnimation(
  animationQue: Point[][],
  totalDuration: number = 600,
  initOpacity: number = 0.3
) {
  // 创建Promise用于异步控制
  let resolve: Function;
  const p = new Promise((res) => (resolve = res));

  let index = 0; // 当前处理的层级索引

  // 计算每个层级的动画持续时间
  // 如果层级超过5层，则按4层计算以避免动画过快
  const peerLevelDuration =
    totalDuration / (animationQue.length > 5 ? 4 : animationQue.length);

  /**
   * 递归执行每个层级的动画
   * @param anQue 当前层级的节点数组
   */
  function peerTimeAnimation(anQue: Point[]) {
    // 检查是否所有层级都已完成
    if (index >= animationQue.length) return resolve();

    // 存储当前层级所有节点的动画Promise
    const ps: Promise<any>[] = [];

    // 为当前层级的每个节点创建进入动画
    anQue.forEach((point) => {
      const p: Promise<any> = pointEnterAnimation(
        point, // 目标节点
        ctx.pointsMesh!, // 节点渲染器
        {
          x: point.coordinate[0], // 目标X坐标
          y: point.coordinate[1], // 目标Y坐标
          z: point.coordinate[2], // 目标Z坐标
          opacity: initOpacity, // 初始透明度
        },
        peerLevelDuration // 动画持续时间
      );
      ps.push(p);
    });

    // 等待当前层级所有节点动画完成后，处理下一层级
    Promise.all(ps).then(() => {
      peerTimeAnimation(animationQue[++index]);
    });
  }

  // 开始执行第一个层级的动画
  peerTimeAnimation(animationQue[index]);
  return p;
}

/**
 * 加载更多数据的渲染函数
 * 处理增量数据加载，包括新节点添加和现有节点位置更新
 * 支持平滑的动画过渡和连线效果
 *
 * @param focusIndex 当前聚焦节点的索引
 * @param diffData 包含新增和更新节点的差异数据
 */
export function renderMoreData(focusIndex: number, diffData: DiffData) {
  // 将新节点加载到节点渲染器中，初始透明度为0
  ctx.pointsMesh?.loadMore(diffData.newPoints, 0);

  // 将新增节点按层级重新组织为二维数组
  const loadedPoints = samePointsLevelToLevelArray(diffData.newPoints);

  // 获取当前聚焦节点的ID和完整信息
  const focusPointId = ctx.pointsMesh!.getPointData(focusIndex)!.id;
  const focusPoint = ctx.graph!.getPointById(focusPointId)!;

  // 调试信息输出
  console.log("loadedPoints", loadedPoints);
  console.log("focusPointId", focusPointId);
  console.log("focusPoint", focusPoint);

  let updateLineFlag = false; // 是否需要更新连线的标志

  // 检查是否需要创建新的连线（当前聚焦节点的子节点刚加载完毕）
  if (!lastFocusChildPoints.length && focusIndex > -1) {
    // 获取聚焦节点的所有子节点
    lastFocusChildPoints = focusPoint.childIds.map(
      (id) => ctx.graph!.getPointById(id)!
    );
    // 创建新的连线渲染器
    ctx.focusLine = new KsgLine(focusPoint, lastFocusChildPoints, 0.1);
    ctx.viewGroup?.add(ctx.focusLine);
    updateLineFlag = true; // 标记需要执行连线动画
  }

  // 如果当前有聚焦节点，将新增节点设置为高亮状态
  if (ctx.pointsMesh!.focusIndex > -1)
    ctx.pointsMesh?.setHightLightPoints(diffData.newPoints);

  // 根据当前模式确定新节点的透明度
  const opacity =
    ctx.model == MODE.MULTIPLE_ROOT
      ? ctx.pointsMesh!.focusIndex > -1
        ? 0.3 // 多根模式下有聚焦时的透明度
        : 1.0 // 多根模式下无聚焦时的透明度
      : 0.3; // 单根模式下的透明度

  // 收集所有需要更新位置的节点的动画Promise
  const updatePointsAnimation: Promise<any>[] = [];
  diffData.updatePoints.forEach((pointInfo) => {
    updatePointsAnimation.push(
      updatePointPositionAnimation(
        ctx.pointsMesh!, // 节点渲染器
        ctx.focusLine!, // 连线渲染器
        focusCrust, // 聚焦外壳
        focusLabel, // 聚焦标签
        pointInfo, // 节点更新信息
        lastFocusChildPoints // 子节点列表
      )
    );
  });

  // 执行完整的加载流程
  Promise.all(updatePointsAnimation)
    .then((points) => {
      // 更新所有位置变化的节点信息
      points.forEach((point) => ctx.pointsMesh?.updatePointInfo(point));
    })
    .then(() =>
      // 执行新加载节点的进入动画
      startPointAnimation(loadedPoints, 1000, opacity)
    )
    .then(() =>
      // 如果需要，执行连线进入动画
      Promise.all(
        lastFocusChildPoints.map(
          (point) =>
            updateLineFlag
              ? lineEnterAnimation(ctx.focusLine!, focusPoint, point, 0.1)
              : null // 如果不需要更新连线，返回null
        )
      )
    )
    .then(() => {
      // 启用连线动态效果
      ctx.focusLine!.enableAnimation = true;
      // 切换子节点为聚焦状态
      ctx.pointsMesh?.toggleFocusChildren(
        lastFocusChildPoints.map((point) => point.index!)
      );
    })
    .then(() => {
      // 如果是全局视图模式，为新节点启用呼吸效果
      if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW) {
        diffData.newPoints.map((point) =>
          ctx.pointsMesh?.enablePointBreath(point.index!)
        );
      }
    });
}

/**
 * 将节点数组按层级转换为二维数组（从顶层到底层）
 * 用于组织动画播放顺序，确保同层节点同时出现，不同层级按顺序出现
 *
 * @param points 需要分组的节点数组
 * @returns 按层级组织的二维节点数组
 */
function samePointsLevelToLevelArray(points: Point[]) {
  // 创建层级映射对象，key为层级编号，value为该层级的节点数组
  const levelMap: { [key: number]: Point[] } = {};

  // 遍历所有节点，按层级分组
  points.forEach((point: Point) => {
    if (!levelMap[point.level]) {
      // 如果该层级还没有节点，创建新数组
      levelMap[point.level] = [point];
    } else {
      // 如果该层级已有节点，添加到现有数组中
      levelMap[point.level].push(point);
    }
  });

  // 将层级映射转换为按层级排序的二维数组
  // Object.entries返回[层级编号, 节点数组]的形式
  return Object.entries(levelMap).map((item) => item[1]);
}

/**
 * 进入子图聚焦模式的渲染函数
 * 当用户点击某个节点进入其子图时调用，负责视角切换和聚焦效果
 * 包含平滑的视角移动、连线切换和聚焦状态管理
 *
 * @param focusInfo 聚焦数据，包含目标节点和其子节点信息
 * @returns Promise 当所有动画和渲染完成时resolve
 */
export function renderFocusData(focusInfo: FocusData): Promise<void> {
  // 调试信息：输出当前图数据和聚焦信息
  console.log("===>", ctx.graph);
  console.log("focusData", focusInfo);

  // 立即显示新聚焦节点的外壳效果和切换聚焦状态
  focusCrust.display(focusInfo.focusPoint);
  ctx.pointsMesh?.toggleFocus(focusInfo.focusPoint.index!);

  return Promise.resolve()
    .then(() => {
      // 第一步：禁用控制器，防止用户在动画期间操作
      ctx.controls!.enabled = false;

      // 清理旧的子节点标签
      focusChildrenLabelManager.clearChildrenLabels();

      // 处理旧连线的离开动画和清理
      if (ctx.focusLine) {
        lineLeaveAnimation(ctx.focusLine!, 300).then(() => {
          // 连线离开动画完成后，从场景中移除并释放资源
          ctx.viewGroup?.remove(ctx.focusLine!);
          ctx.focusLine!.dispose();
        });
      }

      // 更新聚焦子节点缓存
      lastFocusChildPoints = focusInfo.points;
      // 建立新的聚焦子节点索引集合
      ctx.focusChildrenIndex = new Set(
        lastFocusChildPoints.map((point) => point.index!)
      );

      // 第二步：执行视角移动动画
      // 移动相机到聚焦节点上方，Y轴偏移6个单位以获得较好的观察角度
      return viewMoveAnimation(
        ctx.controls!, // 控制器对象
        [
          focusInfo.focusPoint.coordinate[0], // 目标X坐标
          focusInfo.focusPoint.coordinate[1] + 6, // 目标Y坐标（向上偏移6单位）
          focusInfo.focusPoint.coordinate[2], // 目标Z坐标
        ],
        700 // 动画持续时间700ms
      )
        .then(() =>
          // 视角移动完成后显示聚焦标签
          focusLabel.display(focusInfo.focusPoint)
        )
        .then(() => {
          // 第三步：重新启用控制器并创建新连线
          ctx.controls!.enabled = true;

          // 创建新的连线渲染器，连接聚焦节点和其子节点
          ctx.focusLine = new KsgLine(
            focusInfo.focusPoint, // 父节点
            lastFocusChildPoints, // 子节点数组
            0.1 // 连线透明度
          );
          ctx.viewGroup?.add(ctx.focusLine);

          // 并行执行所有子节点的连线进入动画
          return Promise.all(
            lastFocusChildPoints.map((point) =>
              lineEnterAnimation(
                ctx.focusLine!, // 连线渲染器
                focusInfo.focusPoint, // 起始节点
                point, // 目标节点
                0.1 // 连线透明度
              )
            )
          );
        });
    })
    .then(() => {
      // 第四步：完成所有设置
      // 切换子节点为聚焦状态（高亮显示）
      ctx.pointsMesh?.toggleFocusChildren(
        lastFocusChildPoints.map((point) => point.index!)
      );

      // 显示所有子节点的标签
      focusChildrenLabelManager.showChildrenLabels(lastFocusChildPoints);

      // 启用连线的动态效果（如呼吸效果等）
      ctx.focusLine!.enableAnimation = true;
    });
}
