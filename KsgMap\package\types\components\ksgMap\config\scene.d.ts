import { Scene } from "three";
import type { SceneConfig } from "../types";
/**
 * 3D场景配置函数 - 创建和配置Three.js场景
 *
 * Scene是Three.js中的场景对象，它是一个容器，用来保存所有的3D对象、光源、相机等
 * 场景定义了3D世界的环境，包括背景、环境光照、雾效等全局设置
 *
 * 主要功能：
 * - 创建3D场景容器
 * - 设置场景背景和环境贴图
 * - 配置环境光照效果
 * - 创建知识图谱的根容器组
 *
 * @param config 场景配置参数对象
 * @returns 返回包含场景实例的对象
 */
export default function userScene(config: SceneConfig): {
    scene: Scene;
};
