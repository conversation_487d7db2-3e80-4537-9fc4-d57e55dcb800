import { InstancedMesh } from "three";
import { Point } from "../types";
import { Context } from "../ctx";
/**
 * 悬停圆环特效类
 * 继承自Three.js的InstancedMesh，支持高效的实例化渲染
 * 使用自定义着色器实现复杂的扩散动画效果
 */
declare class KsgHoverCircle extends InstancedMesh {
    /** 上一个悬停节点的索引 - 用于避免重复处理 */
    lastIndex: number;
    /**
     * 构造函数 - 初始化悬停特效
     *
     * @param size 特效大小 - 控制圆环的整体尺寸
     */
    constructor(size?: number);
    /**
     * 显示悬停特效并绑定到指定节点
     *
     * 执行流程：
     * 1. 检查是否为重复悬停（优化性能）
     * 2. 重置动画时间
     * 3. 设置特效位置到节点坐标
     * 4. 根据节点状态设置颜色
     * 5. 显示特效并缓存节点索引
     *
     * @param bindPoint 要绑定的悬停节点对象
     */
    display(bindPoint: Point): void;
    /**
     * 隐藏悬停特效
     *
     * 在鼠标移出节点或切换悬停目标时调用
     */
    hide(): void;
    /**
     * 动画更新函数 - 每帧调用
     *
     * 职责：
     * 1. 推进扩散动画的时间进度
     * 2. 计算特效面向相机的朝向
     * 3. 只有在可见时才执行，优化性能
     *
     * @param ctx 上下文对象 - 包含相机和控制器信息
     * @param delta 时间增量 - 两帧之间的时间差
     */
    update(ctx: Partial<Context>, delta?: number): void;
    /**
     * 释放内存资源
     *
     * 在组件销毁时调用，防止内存泄漏
     */
    free(): void;
}
declare const ksgHover: KsgHoverCircle;
export default ksgHover;
