/**
 * 全局视图模块 - enterGlobalView.ts
 *
 * 职责：
 * 1. 处理从聚焦模式切换到全局视图模式
 * 2. 清理聚焦状态的视觉元素（外壳、连线等）
 * 3. 启用节点呼吸动画，增强全局视图的视觉效果
 * 4. 管理视角切换的平滑过渡动画
 *
 * 全局视图特点：
 * - 显示所有节点，无特定聚焦点
 * - 节点具有呼吸动画效果
 * - 没有聚焦连线和外壳装饰
 * - 适合总览整个知识图谱结构
 */
/**
 * 进入全局视图模式的核心函数
 *
 * 执行流程：
 * 1. 清理现有的动画补间效果
 * 2. 检查并防止重复切换
 * 3. 隐藏聚焦装饰元素（外壳、连线）
 * 4. 启用节点呼吸动画
 * 5. 更新历史栈和视角动画
 *
 * @param to 目标视角位置 [x, y, z] - 相机移动的目的地坐标
 * @returns Promise 视角切换动画的异步操作
 */
export default function enterGlobalView(to: [number, number, number]): Promise<unknown> | undefined;
