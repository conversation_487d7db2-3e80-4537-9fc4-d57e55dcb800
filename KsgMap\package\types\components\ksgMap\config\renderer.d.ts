import * as THREE from "three";
import type { RendererConfig } from "../types/index";
/**
 * WebGL渲染器配置函数 - 创建和配置Three.js的WebGL渲染器
 *
 * WebGLRenderer是Three.js的核心渲染器，负责将3D场景渲染到HTML5 Canvas元素上
 * 它使用WebGL API来实现硬件加速的3D图形渲染，提供高性能的图形处理能力
 *
 * 主要功能：
 * - 将3D场景和相机的内容渲染到2D画布上
 * - 处理光照、材质、纹理等视觉效果
 * - 支持抗锯齿、阴影、后处理等高级渲染特性
 *
 * @param config 渲染器配置参数对象
 * @returns 返回包含渲染器实例和DOM元素的对象
 */
export default function useRenderer(config: RendererConfig): {
    renderer: THREE.WebGLRenderer;
    rendererDom: HTMLCanvasElement;
};
