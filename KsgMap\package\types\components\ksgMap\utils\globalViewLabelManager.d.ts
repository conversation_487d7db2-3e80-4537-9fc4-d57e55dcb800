/**
 * 全局视角下的Label管理器
 * 负责在自动旋转过程中智能显示/隐藏节点标签
 */
/**
 * 全局视角Label显示配置
 */
interface GlobalLabelConfig {
    /** 最大显示距离 - 超过此距离的节点不显示标签 */
    maxDistance: number;
    /** 最大深度范围 - 相对于target的前景深度 */
    maxDepth: number;
    /** 视野角度系数 - 控制视锥体检测的严格程度 */
    viewAngleFactor: number;
    /** 更新频率控制 - 每N帧检测一次，优化性能 */
    updateInterval: number;
}
/**
 * 全局视角Label管理器类
 */
export declare class GlobalViewLabelManager {
    /** 配置参数 */
    private config;
    /** 当前显示的标签Map */
    private activeLabels;
    /** 视锥体对象 - 用于检测节点是否在相机视野内 */
    private frustum;
    /** 帧计数器 - 用于控制更新频率 */
    private frameCount;
    /** 是否启用 */
    private enabled;
    constructor(config?: Partial<GlobalLabelConfig>);
    /**
     * 启用全局标签管理
     */
    enable(): void;
    /**
     * 禁用全局标签管理并清理所有标签
     */
    disable(): void;
    /**
     * 每帧更新函数 - 在渲染循环中调用
     * 检测哪些节点应该显示标签，哪些应该隐藏
     */
    update(): void;
    /**
     * 更新相机视锥体
     */
    private updateFrustum;
    /**
     * 检测应该显示标签的节点
     * @param points 所有节点数组
     * @returns 应该显示标签的节点数组
     */
    private detectVisiblePoints;
    /**
     * 更新标签显示状态
     * @param shouldShowPoints 应该显示标签的节点数组
     */
    private updateLabelsDisplay;
    /**
     * 显示节点标签
     * @param point 节点对象
     */
    private showLabel;
    /**
     * 隐藏节点标签
     * @param pointId 节点ID
     * @param label 标签对象
     */
    private hideLabel;
    /**
     * 清理所有标签
     */
    private clearAllLabels;
    /**
     * 更新配置
     * @param newConfig 新的配置参数
     */
    updateConfig(newConfig: Partial<GlobalLabelConfig>): void;
    /**
     * 获取当前显示的标签数量
     */
    getActiveLabelCount(): number;
    /**
     * 获取当前配置
     */
    getConfig(): GlobalLabelConfig;
}
export declare const globalLabelManager: GlobalViewLabelManager;
export {};
