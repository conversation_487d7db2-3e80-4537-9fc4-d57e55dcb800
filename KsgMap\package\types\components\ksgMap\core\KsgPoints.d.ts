import { Points, Color, PerspectiveCamera } from "three";
import { Point } from "../types";
/**
 * 知识节点渲染类 - KsgPoint
 *
 * 继承自Three.js的Points类，专门用于渲染知识图谱中的节点
 * 使用自定义着色器实现高性能的大量节点渲染和特效
 *
 * 主要功能：
 * - 批量渲染大量知识节点（使用GPU加速）
 * - 支持节点的颜色、大小、透明度动态变化
 * - 实现节点的悬停高亮和聚焦效果
 * - 支持呼吸动画等特殊视觉效果
 * - 提供高效的节点查找和状态管理
 *
 * 技术特点：
 * - 使用BufferGeometry优化内存使用
 * - 自定义GLSL着色器实现复杂视觉效果
 * - 加法混合模式创建发光效果
 * - 支持实例化渲染，性能优异
 */
export default class KsgPoint extends Points {
    /** 节点数据数组 - 存储所有节点的完整信息 */
    pointsData: Point[];
    /** ID到索引的映射表 - 用于快速根据节点ID查找对应的数组索引 */
    idIndexMap: {
        [key: string]: number;
    };
    /** 上一个悬停节点的索引 - 用于清除之前的悬停状态 */
    lastHoverIndex: number;
    /** 当前聚焦节点的索引 - 标识用户当前关注的节点 */
    focusIndex: number;
    /** 呼吸动画状态标志 - 控制节点是否执行呼吸效果动画 */
    isBreathAni: boolean;
    /** 聚焦节点的直接子节点索引集合 - 用于高亮显示相关节点 */
    focusChildrenIndexArr: Set<number>;
    /**
     * 构造函数 - 初始化知识节点渲染对象
     *
     * 创建用于渲染大量知识节点的Three.js Points对象
     * 使用BufferGeometry和自定义着色器实现高性能渲染
     *
     * @param points 节点数据数组 - 包含位置、颜色、状态等信息
     * @param total 总节点数量 - 用于预分配缓冲区大小
     * @param opacity 默认透明度 - 节点的初始透明度值 (0-1)
     * @param size 默认大小 - 节点的初始渲染大小
     */
    constructor(points: Point[], total: number, opacity?: number, size?: number);
    /**
     * 释放内存资源
     */
    dispose(): void;
    /**
     * 根据索引获取节点数据
     * @param {number} index 索引
     */
    getPointData(index: number): Point | null;
    /**
     * 根据id查询节点数据
     * @param {string} id id
     */
    getPointDataById(id: string): Point;
    /**
     *更新位置
     @param {number[]} indexArr 索引数组-支持批量操作
     @param {[number, number, number]} position 位置
     */
    updatePosition(indexArr: number[], position: [number, number, number]): void;
    /**
     * 更新颜色
     * @param {number[]} indexArr 索引数组-支持批量操作
     * @param {Color} color 颜色
     */
    updateColor(indexArr: number[], color: Color): void;
    /**
     * 更新尺寸
     * @param {number[]} indexArr 索引数组-支持批量操作
     * @param {number} size 尺寸
     */
    updateSize(indexArr: number[], size: number): void;
    /**
     * 更新透明度
     * @param {number[]} indexArr 索引数组-支持批量操作
     * @param {number} opacity 透明度
     */
    updateOpacity(indexArr: number[], opacity: number): void;
    /**
     * 节点呼吸动画
     * @param {boolean} option 开关
     * @default true 开启状态
     */
    breathAnimationSwitch(option?: boolean): void;
    /**
     * 开启或关闭某个节点的呼吸动画
     * @param index 节点索引
     * @param enable 是否开启
     * @default true 开启状态
     */
    enablePointBreath(index: number, enable?: boolean): void;
    /**
     * 动画更新函数
     */
    update(): void;
    /**
     * 触发某个索引下节点的hover状态
     * @param {number} index 索引
     */
    toggleHover(index?: number): void;
    /**
     *触发某个索引聚焦状态
     *@param {number} index 索引
     */
    toggleFocus(index: number): void;
    /**
     * 初始化高亮的节点,下次聚焦其他
     * 节点时可以在没聚焦状态下自动变暗
     * @param {Point[]} points 节点数组（必须携带index）
     */
    setHightLightPoints(points: Point[]): void;
    /**
     *触发直接前驱状态
     *@param {number[]} indexArr 索引
     */
    toggleFocusChildren(indexArr: number[]): void;
    /**
     * 获取某个索引下节点的dnc坐标
     * @param {number} index 索引
     * @param {PerspectiveCamera} camera 相机
     * @param {number} rendererW 渲染宽度
     * @param {number} rendererH 渲染高度
     */
    getWorldP(index: number, camera: PerspectiveCamera, rendererW: number, rendererH: number): {
        x: number;
        y: number;
    };
    /**
     *加载更多,更新节点
     */
    loadMore(newPointsData: Point[], opacity?: number, size?: number): void;
    /**
     * 更新节点信息
     * @param {Point} point 点信息
     */
    updatePointInfo(point: Point): void;
    /**
     *测试方法-获取渲染的节点真实的3d坐标
     */
    getPoint3DPosition(id: string): number[];
}
