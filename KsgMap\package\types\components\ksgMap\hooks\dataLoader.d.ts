/**
 * 数据加载器 - KsgMap 知识图谱数据管理核心模块
 *
 * 该模块负责知识图谱节点数据的分层加载、缓存管理和状态维护
 * 支持分页加载、数据预处理、错误处理等功能
 * 是 KsgMap 可视化组件的数据基础设施
 */
import type { PointData, getSignalRootApi } from "../types";
import type { Ref } from "@vue/reactivity";
/**
 * 数据加载器 Hook - 处理知识图谱节点数据的分层加载
 *
 * 功能特性：
 * - 支持分层级分页加载知识图谱数据
 * - 内置数据缓存机制，避免重复请求
 * - 自动处理 MathJax 公式渲染
 * - 统一的加载状态管理
 * - 数据预处理和格式化
 *
 * @param api - 数据获取 API 函数，用于请求后端知识图谱数据
 * @param rootId - 根节点 ID，作为数据加载的起始点
 * @param loading - 响应式加载状态引用，管理 loading/loaded/error 状态
 * @param crt - 当前加载层级，默认从第 1 层开始
 * @param levelSize - 每次加载的层级数量，默认加载 2 层数据
 * @returns 包含初始化、加载更多数据方法的对象
 */
export default function dataLoader(api: getSignalRootApi, rootId: string, loading: Ref<"loading" | "loaded" | "error">, crt?: number, levelSize?: number): {
    init: () => Promise<{
        data: PointData[];
        pager: {
            current: number;
            levelSize: number;
            total: number;
        };
        rootId: string;
    }>;
    loadMore: (rootId: string, current: number, levelSize: number) => Promise<PointData[] | undefined>;
    loadMoreNew: (rootId: string, current: number, levelSize: number) => Promise<PointData[] | undefined>;
};
