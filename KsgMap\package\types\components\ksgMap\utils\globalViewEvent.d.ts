/**
 * 创建鼠标移动事件处理器
 * 用于处理全局视图的鼠标移动事件，支持防抖功能
 * 当鼠标移动时立即触发移动事件，当鼠标停止移动一段时间后触发结束事件
 *
 * @param onMove 移动时触发的事件回调函数，鼠标开始移动时立即调用
 * @param onMoveEnd 移动结束后并且在等待一段时间内没有进行任何操作后触发的事件回调函数
 * @param moveEndDelay 移动结束后等待时间（毫秒），默认100ms
 * @returns 包含事件处理函数和清理函数的对象
 */
export declare function createMouseMoveEvent(onMove: () => void, onMoveEnd: () => void, moveEndDelay?: number): {
    handleMoveEvent: (event: MouseEvent) => void;
    clear: () => void;
};
