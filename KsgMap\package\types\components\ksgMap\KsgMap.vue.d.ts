import { type PropType } from "vue";
import { loadMorePointsData } from "./core/loadData";
import { type Options } from "./config/index";
import type { PointData } from "./types";
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    /** 组件宽度，支持数字或字符串格式 */
    width: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
        required: false;
    };
    /** 组件高度，支持数字或字符串格式 */
    height: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
        required: false;
    };
    /** Three.js 相关配置选项 */
    config: {
        type: PropType<Options>;
        default: () => {};
        required: false;
    };
    /** 加载状态：loading-加载中, loaded-已加载, error-错误 */
    loading: {
        type: PropType<"loading" | "loaded" | "error">;
        default: string;
    };
}>, {
    /**
     * 首次加载知识点数据
     * @param pointsData - 知识点数据数组
     * @param total - 数据总数
     * @param rootId - 根节点ID（可选）
     * @returns Promise，完成加载后resolve
     */
    firstLoadPointsData: (pointsData: PointData[], total: number, rootId?: string) => Promise<void>;
    /**
     * 加载更多知识点数据
     * 用于分页加载或动态扩展数据
     */
    loadMorePointsData: typeof loadMorePointsData;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {} & {
    loadMore: (rootId: string, current: number, levelSize: number) => any;
    clickLabel: (id: string) => any;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    /** 组件宽度，支持数字或字符串格式 */
    width: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
        required: false;
    };
    /** 组件高度，支持数字或字符串格式 */
    height: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
        required: false;
    };
    /** Three.js 相关配置选项 */
    config: {
        type: PropType<Options>;
        default: () => {};
        required: false;
    };
    /** 加载状态：loading-加载中, loaded-已加载, error-错误 */
    loading: {
        type: PropType<"loading" | "loaded" | "error">;
        default: string;
    };
}>> & Readonly<{
    onLoadMore?: ((rootId: string, current: number, levelSize: number) => any) | undefined;
    onClickLabel?: ((id: string) => any) | undefined;
}>, {
    width: string | number;
    height: string | number;
    config: Options;
    loading: "error" | "loading" | "loaded";
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
