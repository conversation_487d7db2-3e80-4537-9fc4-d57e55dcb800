import { Object3D, PerspectiveCamera, Group, Color } from "three";
import { POINT_STUDY_STATUS } from "../enums";
import KsgGraph from "../core/KsgGraph";
/**
 * 为数值添加CSS单位
 * 如果传入的值已经包含单位（px、%、rem、em），则直接返回
 * 否则为纯数字添加px单位
 * @param value 需要添加单位的值，可以是字符串或数字
 * @returns 带有单位的字符串
 */
export declare function addUnit(value?: string | number): string;
/**
 * 根据点的学习状态获取对应的RGB颜色值
 * 将十六进制颜色值转换为Three.js着色器所需的RGB数组格式（0-1范围）
 * @param pointStatus 点的学习状态枚举值
 * @returns RGB数组，每个分量的值在0-1之间
 */
export declare function pointStatusToColor(pointStatus: POINT_STUDY_STATUS): [number, number, number];
/** 控制器相关辅助函数 */
/**
 * 获取鼠标点击区域对应的3D对象ID
 * 使用射线投射检测鼠标点击位置对应的3D对象
 * @param event 鼠标事件对象
 * @param dom Canvas DOM元素
 * @param subareas 包含可点击子区域的3D对象组
 * @param camera 透视相机对象
 * @returns 点击对象的ID，如果未点击到有效对象则返回null
 */
export declare function getClickArea(event: MouseEvent, dom: HTMLCanvasElement, subareas: Object3D, camera: PerspectiveCamera): any;
/**
 * 线性映射函数
 * 将值从输入范围映射到输出范围
 * @param v 要映射的值
 * @param i1 输入范围的最小值
 * @param i2 输入范围的最大值
 * @param o1 输出范围的最小值
 * @param o2 输出范围的最大值
 * @returns 映射后的值
 */
export declare function map(v: number, i1: number, i2: number, o1: number, o2: number): number;
/**
 * 约束函数，将值限制在指定范围内
 * @param v 要约束的值
 * @param min 最小值
 * @param max 最大值
 * @returns 约束后的值
 */
export declare function constrain(v: number, min: number, max: number): number;
/**
 * 分段线性映射函数
 * 将一个数值从一个范围映射到另一个范围，支持多段映射
 *
 * 使用线性插值将输入值 `val` 从 `from` 数组定义的范围映射到 `to` 数组定义的范围
 * `from` 和 `to` 数组必须具有相同的长度，并且至少包含两个元素
 * `from` 数组必须单调递增
 *
 * @param val 要映射的值
 * @param from 输入范围数组，必须单调递增
 * @param to 输出范围数组，与from数组一一对应
 * @returns 映射后的值
 * @throws 当from和to数组长度不一致或长度小于2时抛出错误
 */
export declare function cmap(val: number, from: number[], to: number[]): number;
/**
 * 判断两个数字是否近似相等
 * 使用小的误差值来比较浮点数，避免精度问题
 * @param a 第一个数字
 * @param b 第二个数字
 * @returns 如果两个数字的差值小于0.0001则返回true
 */
export declare function equiv(a: number, b: number): boolean;
/**
 * 设置嵌套对象属性
 * 根据点分隔的路径字符串设置对象的嵌套属性值
 * 如果路径中的对象不存在，会自动创建
 * @param obj 目标对象
 * @param path 属性路径，使用点分隔（如："a.b.c"）
 * @param value 要设置的值
 */
export declare function setNestedProperty(obj: any, path: string, value: any): void;
/**
 * 创建悬停点事件处理函数（旧版本，已被新版本替代）
 * 用于处理3D场景中点对象的鼠标悬停事件
 *
 * @param el 容器HTML元素
 * @param camera 透视相机对象
 * @param viewGroup 包含可交互对象的组
 * @param enterCallback 鼠标进入对象时的回调函数
 * @param leaveCallback 鼠标离开对象时的回调函数
 * @returns 包含事件处理和清理方法的对象
 */
export declare function createHoverPointEventFun(el: HTMLElement, camera: PerspectiveCamera, viewGroup: Group, enterCallback: (data: any) => void, leaveCallback: (data: any) => void): {
    clear: () => void;
    event: (e: MouseEvent) => void;
};
/**
 * 获取图中最大的Y值
 * 根据图的最大层级和层级间距计算场景的最大Y坐标
 * @param graph 图数据结构
 * @param levelSpace 层级间距
 * @returns 最大的Y值（绝对值）
 */
export declare function getMaxY(graph: KsgGraph, levelSpace: number): number;
/**
 * 获取指定坐标位置的DOM元素
 * @param e 鼠标事件对象
 * @returns 该坐标位置的DOM元素
 */
export declare function getDomElement(e: MouseEvent): HTMLElement;
/**
 * 递归查找有效的父节点
 * 向上遍历DOM树，查找具有css2d-label类名的DIV元素
 * @param ele DOM元素节点
 * @returns 找到的标签元素的ID，如果没找到则返回null
 */
export declare function findValidateParentNode(ele: Node | null): string | null;
/**
 * 计算DOM元素的宽高（未挂载到文档时）
 * 临时将元素添加到body中进行尺寸计算，然后移除
 * 适用于计算标签元素在渲染前的尺寸
 * @param el 需要计算尺寸的DOM元素
 * @returns 包含宽度和高度的对象
 */
export declare function computedWH(el: Element): {
    width: number;
    height: number;
};
/**
 * 计算3D对象在屏幕视口中的像素坐标位置
 * 将3D世界坐标转换为2D屏幕坐标
 * @param object 需要计算位置的3D对象
 * @param camera 透视相机对象
 * @param rendererW 渲染器宽度
 * @param rendererH 渲染器高度
 * @returns 屏幕坐标位置，相对于视口的x,y坐标
 */
export declare function getObjectPosition(object: Object3D, camera: PerspectiveCamera, rendererW: number, rendererH: number): {
    x: number;
    y: number;
};
/**
 * 根据知识点掌握状态获取对应的Three.js颜色对象
 * @param studyStatus 知识点学习状态枚举值
 * @returns Three.js Color对象
 */
export declare function studyStatusToColor(studyStatus: POINT_STUDY_STATUS): Color;
