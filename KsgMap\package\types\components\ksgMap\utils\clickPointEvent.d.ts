import type { Point, Size } from "../types";
import { type Context } from "../ctx";
/**
 * 创建点击事件处理器
 * 使用Three.js的射线投射检测用户点击的3D点对象
 * @param elSize 容器元素的尺寸信息
 * @param ctx 应用上下文，包含相机、网格等对象
 * @param clickedPointCallback 点击点对象时的回调函数
 * @returns 包含事件处理、清理和尺寸更新方法的对象
 */
export default function createClickEvent(elSize: Size, ctx: Partial<Context>, clickedPointCallback: (data: Point) => void): {
    clear: () => void;
    event: (e: MouseEvent) => void;
    updateSize: (w: number, h: number) => void;
};
