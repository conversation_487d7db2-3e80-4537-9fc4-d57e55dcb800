import { KsgLabel } from "./KsgLabel";
import type { Point } from "../types";
/**
 * 聚焦子节点标签管理器
 *
 * 职责：
 * 1. 管理聚焦状态下所有子节点的标签显示
 * 2. 提供子节点标签的显示、隐藏和清理功能
 * 3. 确保标签的生命周期管理和内存释放
 * 4. 与聚焦系统协调工作，避免标签冲突
 *
 * 使用场景：
 * - 当用户点击节点进入聚焦状态时，显示所有子节点的标签
 * - 当切换到新的聚焦节点时，清理旧的子节点标签
 * - 当退出聚焦模式时，清理所有子节点标签
 */
export declare class FocusChildrenLabelManager {
    /** 当前活跃的子节点标签集合 - 使用Map存储节点ID到标签实例的映射 */
    private activeLabels;
    /** 是否启用子节点标签显示 */
    private enabled;
    /**
     * 显示子节点标签
     *
     * 为传入的子节点数组中的每个节点创建并显示标签
     * 标签会自动添加到3D场景中，并进行生命周期管理
     *
     * @param childrenPoints 子节点数组 - 需要显示标签的节点列表
     */
    showChildrenLabels(childrenPoints: Point[]): void;
    /**
     * 隐藏所有子节点标签
     *
     * 隐藏当前显示的所有子节点标签，但不销毁标签实例
     * 适用于临时隐藏标签的场景
     */
    hideChildrenLabels(): void;
    /**
     * 清理所有子节点标签
     *
     * 完全清理当前的所有子节点标签，包括：
     * 1. 隐藏标签
     * 2. 从3D场景中移除
     * 3. 清空内部标签集合
     *
     * 适用于切换聚焦节点或退出聚焦模式时的清理工作
     */
    clearChildrenLabels(): void;
    /**
     * 更新指定子节点的标签位置
     *
     * 当子节点位置发生变化时（如动画过程中），同步更新对应标签的位置
     *
     * @param pointId 节点ID
     * @param newPosition 新的3D坐标 [x, y, z]
     */
    updateChildLabelPosition(pointId: string, newPosition: [number, number, number]): void;
    /**
     * 启用或禁用子节点标签功能
     *
     * @param enabled 是否启用
     */
    setEnabled(enabled: boolean): void;
    /**
     * 获取当前活跃的子节点标签数量
     *
     * @returns 当前显示的子节点标签数量
     */
    getActiveLabelCount(): number;
    /**
     * 检查指定节点是否有活跃的标签
     *
     * @param pointId 节点ID
     * @returns 是否存在对应的标签
     */
    hasActiveLabel(pointId: string): boolean;
    /**
     * 获取指定节点的标签实例
     *
     * @param pointId 节点ID
     * @returns 标签实例，如果不存在则返回undefined
     */
    getLabel(pointId: string): KsgLabel | undefined;
    /**
     * 销毁管理器并清理所有资源
     *
     * 在组件卸载或系统关闭时调用，确保没有内存泄漏
     */
    dispose(): void;
}
declare const focusChildrenLabelManager: FocusChildrenLabelManager;
export default focusChildrenLabelManager;
