# 子节点标签功能使用指南

## 功能介绍

子节点标签功能是知识图谱交互系统的重要增强功能，当用户点击节点进入聚焦状态时，系统会自动显示该节点所有子元素的标签，提供更丰富的信息展示。

## 功能效果

### 聚焦前
- 只能看到节点的视觉表示（圆点）
- 需要悬停才能看到节点名称

### 聚焦后
- 聚焦节点显示标签
- **所有子节点同时显示标签** ✨
- 子节点高亮显示
- 连线动画效果

## 使用方法

### 基本操作

1. **进入聚焦状态**
   - 点击任意节点
   - 系统自动进入聚焦模式
   - 显示聚焦节点和所有子节点的标签

2. **切换聚焦节点**
   - 点击其他节点
   - 自动清理旧标签，显示新标签

3. **退出聚焦状态**
   - 双击空白区域进入全局视图
   - 所有子节点标签自动清理

4. **历史回退**
   - 使用回退功能
   - 标签状态自动同步更新

## 功能特性

### 🎯 智能标签管理
- **自动显示**：进入聚焦状态时自动显示所有子节点标签
- **自动清理**：切换状态时自动清理不需要的标签
- **避免重复**：智能检测，避免重复创建相同标签

### 🚀 性能优化
- **批量操作**：支持批量显示和隐藏标签
- **内存管理**：及时释放不需要的标签实例
- **场景集成**：与3D场景无缝集成

### 🎨 视觉体验
- **一致样式**：子节点标签与聚焦节点标签样式一致
- **流畅动画**：标签显示与聚焦动画协调进行
- **清晰布局**：标签位置智能计算，避免重叠

## 配置选项

### 启用/禁用功能

```typescript
import focusChildrenLabelManager from './core/FocusChildrenLabelManager';

// 禁用子节点标签功能
focusChildrenLabelManager.setEnabled(false);

// 启用子节点标签功能（默认启用）
focusChildrenLabelManager.setEnabled(true);
```

### 获取状态信息

```typescript
// 获取当前活跃标签数量
const count = focusChildrenLabelManager.getActiveLabelCount();

// 检查特定节点是否有标签
const hasLabel = focusChildrenLabelManager.hasActiveLabel('node-id');

// 获取特定节点的标签实例
const label = focusChildrenLabelManager.getLabel('node-id');
```

## 样式自定义

### CSS 样式配置

```css
/* 子节点标签基础样式 */
.css2d-label {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: Arial, sans-serif;
  pointer-events: auto;
  user-select: none;
}

/* 标签内容样式 */
.css2d-label-inner {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

/* 标签动画效果 */
.css2d-label {
  transition: opacity 0.3s ease-in-out;
}

/* 聚焦状态下的特殊样式 */
.css2d-label.focus-child {
  border: 1px solid #4CAF50;
  background: rgba(76, 175, 80, 0.1);
}
```

### 动画配置

```css
/* 标签进入动画 */
@keyframes labelFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 标签离开动画 */
@keyframes labelFadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}
```

## 最佳实践

### 1. 性能优化建议

```typescript
// 对于大量子节点，考虑限制显示数量
const MAX_LABELS = 10;
const limitedChildPoints = childPoints.slice(0, MAX_LABELS);
focusChildrenLabelManager.showChildrenLabels(limitedChildPoints);
```

### 2. 错误处理

```typescript
try {
  focusChildrenLabelManager.showChildrenLabels(childPoints);
} catch (error) {
  console.warn('显示子节点标签失败:', error);
  // 降级处理：只显示聚焦节点标签
}
```

### 3. 调试信息

```typescript
// 开发环境下添加调试信息
if (process.env.NODE_ENV === 'development') {
  console.log('当前活跃标签数量:', focusChildrenLabelManager.getActiveLabelCount());
}
```

## 常见问题

### Q: 标签不显示怎么办？
**A:** 检查以下几点：
1. 确认功能已启用：`focusChildrenLabelManager.setEnabled(true)`
2. 检查子节点数据是否正确
3. 确认3D场景（viewGroup）是否正常初始化

### Q: 标签显示位置不正确？
**A:** 标签位置基于节点的3D坐标自动计算，如果位置不正确：
1. 检查节点的 `coordinate` 属性是否正确
2. 确认相机和场景的变换矩阵是否正常

### Q: 性能问题怎么解决？
**A:** 对于大量子节点的情况：
1. 限制同时显示的标签数量
2. 使用延迟加载机制
3. 考虑实现标签的LOD（细节层次）系统

### Q: 如何自定义标签样式？
**A:** 通过CSS修改 `.css2d-label` 相关样式类，或者扩展 `KsgLabel` 类实现自定义标签。

## 技术支持

如果遇到问题或需要功能扩展，请参考：
- [技术实现文档](./子节点标签显示功能实现说明.md)
- [API 文档](../types/components/ksgMap/core/FocusChildrenLabelManager.d.ts)
- [测试用例](../test/focusChildrenLabels.test.ts)

## 更新日志

### v1.0.0
- ✅ 实现基础的子节点标签显示功能
- ✅ 集成到聚焦系统中
- ✅ 添加完整的生命周期管理
- ✅ 提供配置选项和样式自定义

### 计划中的功能
- 🔄 标签交互功能（点击、悬停）
- 🔄 标签显示优先级设置
- 🔄 更丰富的动画效果
- 🔄 标签内容的自定义格式化
