import { KsgControls } from "../core/KsgControls";
import type { ControlsConfig } from "../types/index";
/**
 * 相机控制器配置函数 - 创建和配置自定义的相机控制器
 *
 * KsgControls是基于Three.js OrbitControls扩展的自定义控制器
 * 专门为知识图谱场景设计，提供了更精确的相机控制和交互体验
 *
 * 主要功能：
 * - 鼠标/触摸控制相机的旋转、缩放、平移
 * - 限制相机的移动范围和角度
 * - 支持阻尼效果，提供平滑的交互体验
 * - 针对知识图谱场景优化的控制逻辑
 *
 * @param option 控制器配置参数对象
 * @returns 返回包含控制器实例的对象
 */
export default function useControls(option: ControlsConfig): {
    controls: KsgControls;
};
