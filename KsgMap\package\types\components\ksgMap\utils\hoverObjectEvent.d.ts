import type { Size, Point } from "../types";
import { type Context } from "../ctx";
/**
 * 创建悬停点事件处理函数
 * 用于处理3D场景中点对象的鼠标悬停事件，支持进入和离开回调
 * 使用射线投射检测鼠标位置对应的点对象
 *
 * @param elSize 容器元素的尺寸信息
 * @param ctx 应用上下文，包含相机、点网格等对象
 * @param enterCallback 鼠标进入点对象时的回调函数
 * @param leaveCallback 鼠标离开点对象时的回调函数
 * @returns 包含事件处理、清理和尺寸更新方法的对象
 */
export default function createHoverPointEventFun(elSize: Size, ctx: Partial<Context>, enterCallback: (data: Point) => void, leaveCallback: () => void): {
    clear: () => void;
    event: (e: MouseEvent) => void;
    updateSize: (w: number, h: number) => void;
};
