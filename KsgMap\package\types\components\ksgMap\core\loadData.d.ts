import type { PointData } from "../types";
/**
 * 数据加载器模块 - loadData.ts
 *
 * 职责：
 * 1. 处理初始节点数据的加载和渲染
 * 2. 管理增量数据的动态加载
 * 3. 协调图计算与渲染的执行流程
 * 4. 根据不同模式选择相应的渲染策略
 *
 * 核心流程：
 * pointsData → KsgGraph计算 → frameScheduler异步执行 → 渲染模式分发
 */
/**
 * 加载知识点数据 - 系统初始化的入口函数
 *
 * 执行流程：
 * 1. 创建KsgGraph实例，开始图布局计算
 * 2. 等待frameScheduler异步计算完成
 * 3. 更新相机控制器的Y轴范围限制
 * 4. 根据模式选择渲染策略（单根/多根）
 *
 * @param pointsData 原始节点数据数组 - 包含节点关系和属性信息
 * @param totalPoints 节点总数量 - 用于内存预分配和分页控制
 * @param rootId 根节点ID - 单根模式下的聚焦起点
 */
export declare function loadPointsData(pointsData: PointData[], totalPoints: number, rootId?: string): Promise<void>;
/**
 * 加载更多数据 - 增量数据加载函数
 *
 * 用于懒加载场景，当用户需要查看更多节点时调用
 * 支持增量更新，避免重新计算整个图结构
 *
 * 执行流程：
 * 1. 调用graph.loadMore()进行增量计算
 * 2. 获取差异数据（新增节点 + 位置变化节点）
 * 3. 更新相机控制范围
 * 4. 触发增量渲染动画
 *
 * @param pointsData 新增的节点数据数组
 */
export declare function loadMorePointsData(pointsData: PointData[]): void;
