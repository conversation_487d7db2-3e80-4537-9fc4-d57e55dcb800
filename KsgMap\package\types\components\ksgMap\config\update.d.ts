/**
 * KsgMap 画布尺寸更新模块
 *
 * 该模块负责处理画布容器尺寸变化时的相关更新操作，
 * 包括渲染器尺寸、相机宽高比、视口范围等的同步更新。
 *
 */
/**
 * 画布尺寸更新函数
 * 当画布容器尺寸发生变化时，调用此函数来更新相关的渲染配置。
 * 该函数会同步更新以下内容：
 * - WebGL渲染器尺寸
 * - CSS2D渲染器尺寸
 * - 相机宽高比和投影矩阵
 * - 视口范围坐标
 *
 * @param wrapperEle - 画布容器的DOM元素
 * @returns 返回更新后的宽度和高度，如果必要的渲染器未初始化则返回undefined
 *
 */
export default function useUpdate(wrapperEle: HTMLElement): {
    /** 画布宽度（像素） */
    width: number;
    /** 画布高度（像素） */
    height: number;
} | undefined;
