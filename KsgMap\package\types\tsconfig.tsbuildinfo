{"root": ["../../src/app.vue", "../../src/global.d.ts", "../../src/main.ts", "../../src/vite-env.d.ts", "../../src/components/ksgmap/ksgmap.vue", "../../src/components/ksgmap/index.ts", "../../src/components/ksgmap/animation/ksganimation.ts", "../../src/components/ksgmap/animation/enterfocus.ts", "../../src/components/ksgmap/animation/enterglobal.ts", "../../src/components/ksgmap/animation/label.ts", "../../src/components/ksgmap/animation/load.ts", "../../src/components/ksgmap/animation/loadmore.ts", "../../src/components/ksgmap/animation/mark.ts", "../../src/components/ksgmap/animation/point.ts", "../../src/components/ksgmap/config/camera.ts", "../../src/components/ksgmap/config/controls.ts", "../../src/components/ksgmap/config/css2drenderer.ts", "../../src/components/ksgmap/config/event.ts", "../../src/components/ksgmap/config/index.ts", "../../src/components/ksgmap/config/renderer.ts", "../../src/components/ksgmap/config/scene.ts", "../../src/components/ksgmap/config/update.ts", "../../src/components/ksgmap/core/focuschildrenlabelmanager.ts", "../../src/components/ksgmap/core/ksgcontrols.ts", "../../src/components/ksgmap/core/ksggraph.ts", "../../src/components/ksgmap/core/ksghover.ts", "../../src/components/ksgmap/core/ksglabel.ts", "../../src/components/ksgmap/core/ksgline.ts", "../../src/components/ksgmap/core/ksgpoints.ts", "../../src/components/ksgmap/core/enterfocus.ts", "../../src/components/ksgmap/core/enterglobalview.ts", "../../src/components/ksgmap/core/focuscrust.ts", "../../src/components/ksgmap/core/loaddata.ts", "../../src/components/ksgmap/core/renderdata.ts", "../../src/components/ksgmap/ctx/index.ts", "../../src/components/ksgmap/enums/index.ts", "../../src/components/ksgmap/hooks/dataloader.ts", "../../src/components/ksgmap/hooks/userendererframe.ts", "../../src/components/ksgmap/types/index.ts", "../../src/components/ksgmap/utils/framescheduler.ts", "../../src/components/ksgmap/utils/clickpointevent.ts", "../../src/components/ksgmap/utils/clonepoint.ts", "../../src/components/ksgmap/utils/globalviewevent.ts", "../../src/components/ksgmap/utils/globalviewlabelmanager.ts", "../../src/components/ksgmap/utils/hoverobjectevent.ts", "../../src/components/ksgmap/utils/index.ts", "../../src/components/ksgmap/utils/mathjax.ts", "../../src/components/ksgmap/utils/viewvalidate.ts", "../../src/components/ksgmap/workers/calculategraph.ts", "../../src/components/ksgmap/workers/worker.ts", "../../src/network/api.ts"], "errors": true, "version": "5.7.3"}