import type { RendererConfig } from "../types";
/**
 * 简化的配置类型 - 用于基本的CSS2D渲染器配置
 */
type Config = {
    width: number;
    height: number;
    container: HTMLElement;
};
/**
 * CSS2D渲染器配置函数 - 创建和配置Three.js的CSS2D渲染器
 *
 * CSS2DRenderer是Three.js的特殊渲染器，用于在3D场景中渲染HTML/CSS元素
 * 它可以将HTML元素（如文本标签、按钮等）精确地定位到3D空间中的特定位置
 *
 * 主要功能：
 * - 在3D场景中渲染HTML元素（标签、文本等）
 * - 自动处理HTML元素的3D位置转换为2D屏幕坐标
 * - 支持HTML元素的深度测试和遮挡关系
 * - 提供比WebGL文本渲染更丰富的样式和交互能力
 *
 * 应用场景：
 * - 知识节点的文本标签显示
 * - 3D场景中的UI界面元素
 * - 需要复杂样式的文本内容
 *
 * @param config 渲染器配置参数，支持简化配置或完整配置
 * @returns 返回包含CSS2D渲染器DOM元素的对象
 */
export default function useCSS2DRender(config: Config | RendererConfig): {
    css2dRendererDom: HTMLElement | undefined;
};
export {};
