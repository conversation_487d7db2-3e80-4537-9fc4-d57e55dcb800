import type { Point } from "../types";
import { ENTER_FOCUS_MODE } from "../enums";
/**
 * 聚焦模式处理模块 - enterFocus.ts
 *
 * 职责：
 * 1. 处理用户点击节点进入聚焦状态的逻辑
 * 2. 管理聚焦历史栈，支持前进后退操作
 * 3. 构建聚焦数据结构，协调视图切换
 * 4. 控制聚焦动画和子图渲染
 *
 * 核心概念：
 * - 聚焦状态：以某个节点为中心，显示其直接子节点的视图模式
 * - 历史栈：记录用户的浏览路径，支持回退操作
 * - 子图：聚焦节点及其直接子节点组成的局部图结构
 */
/**
 * 处理进入聚焦模式的核心函数
 *
 * 执行流程：
 * 1. 验证点击节点的有效性（避免重复聚焦）
 * 2. 从图数据中获取真实的节点信息
 * 3. 构建聚焦数据结构（聚焦节点 + 子节点集合）
 * 4. 更新历史栈（支持前进后退）
 * 5. 触发聚焦渲染动画
 *
 * @param point 被点击的知识节点对象
 * @param mode 进入模式 - ENTER：正常进入，BACK：历史回退
 * @returns Promise<void> 异步操作完成的承诺
 */
export declare function handleEnterFocus(point: Point, mode?: ENTER_FOCUS_MODE): Promise<void>;
